# -*- coding: utf-8 -*-
import os
import threading
import time
import json
import random
import re
import concurrent.futures
import requests
import tkinter as tk
from tkinter import filedialog, messagebox
from colorama import init, Fore, Style
import sys


# 初始化colorama以支持彩色输出
init()

class ErrorCollector:
    """错误收集器，用于收集和管理处理失败的文件"""

    def __init__(self):
        self.failed_files = []  # 存储失败的文件信息
        self.retry_counts = {}  # 记录每个文件的重试次数
        self.max_retries = 3    # 最大重试次数
        self.lock = threading.Lock()  # 线程安全锁

    def add_failed_file(self, index, filename, error_type, error_message):
        """添加失败的文件到重试队列"""
        with self.lock:
            retry_count = self.retry_counts.get(filename, 0)
            if retry_count < self.max_retries:
                self.failed_files.append({
                    'index': index,
                    'filename': filename,
                    'error_type': error_type,
                    'error_message': error_message,
                    'retry_count': retry_count
                })
                self.retry_counts[filename] = retry_count + 1
                return True
            return False

    def get_retry_files(self):
        """获取需要重试的文件列表"""
        with self.lock:
            retry_files = self.failed_files.copy()
            self.failed_files.clear()
            return retry_files

    def has_retry_files(self):
        """检查是否有需要重试的文件"""
        with self.lock:
            return len(self.failed_files) > 0

    def get_retry_count(self, filename):
        """获取文件的重试次数"""
        return self.retry_counts.get(filename, 0)

    def clear(self):
        """清空错误收集器"""
        with self.lock:
            self.failed_files.clear()
            self.retry_counts.clear()

    def get_total_failed_count(self):
        """获取总的失败文件数量（包括已重试的）"""
        return len(self.retry_counts)

    def get_final_failed_files(self):
        """获取最终仍然失败的文件列表"""
        final_failed = []
        with self.lock:
            for filename, retry_count in self.retry_counts.items():
                if retry_count >= self.max_retries:
                    final_failed.append(filename)
        return final_failed



class AINameGenerator:
    def __init__(self):
        # 初始化基本变量
        self.thread_count = 50  # 默认线程数
        self.current_index = 0
        self.file_list = []
        self.results = []
        self.is_processing = False
        self.input_tokens = 0
        self.output_tokens = 0
        self.current_folder = None
        self.processed_count = 0
        self.user_balance = "未知"
        # 移除了api_clients，直接使用密钥
        self.active_keys = []  # 存储有效的API密钥
        self.keys_status = {}  # 存储每个密钥的状态和余额
        self.client_index = 0  # 当前使用的客户端索引
        self.client_lock = threading.Lock()  # 用于线程安全地选择客户端
        self.key_usage_count = {}  # 记录每个密钥的使用次数
        self.error_folder = None  # 存储错误文件夹路径
        self.error_collector = ErrorCollector()  # 错误收集器

        # 初始化配置变量
        self.PREFIX_OPTIONS = [
            "2D Flat Rotating Acrylic Desktop Ornament",
            "2D Flat Revolving Acrylic Desktop Ornament",
            "2D Flat Rotatable Acrylic Desktop Ornament"
        ]
        self.STYLE_SUFFIXES = [
            'Elegant', 'Whimsical', 'Artistic', 'Charming', 'Sophisticated',
            'Playful', 'Minimalist', 'Timeless', 'Unique', 'Inspiring',
            'Vibrant', 'Serene', 'Majestic', 'Nostalgic', 'Futuristic',
            'Ethereal', 'Rustic', 'Modern', 'Classic', 'Dynamic',
            'Tranquil', 'Bold', 'Delicate', 'Mystical', 'Quirky',
            'Radiant', 'Exquisite', 'Luminous', 'Enchanting', 'Harmonious'
        ]

        # 加载配置文件
        self.config = self._load_config()

        # 加载API密钥
        self._load_api_keys()

    def _load_api_keys(self):
        """加载API密钥"""
        try:
            # 从api_keys.txt文件读取API密钥
            api_keys = []
            try:
                with open('api_keys.txt', 'r', encoding='utf-8') as f:
                    for line in f:
                        key = line.strip()
                        if key and key.startswith('sk-'):
                            api_keys.append(key)
                print(f"{Fore.GREEN}✓ 从api_keys.txt加载了 {len(api_keys)} 个API密钥{Style.RESET_ALL}")
            except FileNotFoundError:
                print(f"{Fore.RED}✗ 未找到api_keys.txt文件{Style.RESET_ALL}")
                return []

            if not api_keys:
                print(f"{Fore.RED}✗ api_keys.txt文件中未找到有效的API密钥{Style.RESET_ALL}")
                return []

            self.active_keys = api_keys
            self.keys_status = {}
            for key in api_keys:
                self.keys_status[key] = {"balance": 0, "name": "用户"}

            return api_keys

        except Exception as e:
            print(f"{Fore.RED}✗ 加载API密钥失败: {str(e)}{Style.RESET_ALL}")
            return []

    def call_api(self, message, api_key):
        """直接调用API"""
        url = self.config.get('api_base_url', 'https://api.siliconflow.cn/v1') + '/chat/completions'

        # 加载系统提示
        system_prompt = '\n'.join(self.config['system_prompt'])
        user_content = self.config['user_prompt'].format(filename=message)

        payload = {
            "model": self.config['model'],
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_content}
            ],
            "stream": False,
            "max_tokens": self.config['max_tokens'],
            "temperature": self.config['temperature'],
            "top_p": 0.7,
            "top_k": 50,
            "frequency_penalty": 0.5,
            "n": 1,
            "stop": []
        }

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()

        result = response.json()
        return result['choices'][0]['message']['content'].strip(), result.get('usage', {})

    def _load_config(self):
        """加载配置文件"""
        try:
            with open('vdf.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"{Fore.GREEN}✓ 配置文件加载成功{Style.RESET_ALL}")
            return config
        except Exception as e:
            print(f"{Fore.RED}✗ 配置文件加载失败：{str(e)}{Style.RESET_ALL}")
            return {}



    def get_next_key(self):
        """线程安全地获取下一个API密钥"""
        with self.client_lock:
            current_key = self.active_keys[self.client_index]
            self.client_index = (self.client_index + 1) % len(self.active_keys)
            # 记录密钥使用次数
            self.key_usage_count[current_key] = self.key_usage_count.get(current_key, 0) + 1
            return current_key

    def select_folder(self):
        """选择要处理的文件夹"""
        print(f"\n{Fore.CYAN}▌ 文件夹选择 ▐{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}> 即将打开文件夹选择对话框，请选择包含需要重命名文件的文件夹{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}> 提示：建议选择仅包含目标文件的专用文件夹，以避免意外处理其他文件{Style.RESET_ALL}")
        
        # 初始化tkinter
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 使用messagebox替代input
        messagebox.showinfo("准备选择文件夹", "点击确定按钮后将打开文件夹选择对话框")
        
        folder_path = filedialog.askdirectory(title="选择需要处理的文件夹")
        root.destroy()
        
        if not folder_path:
            print(f"{Fore.RED}✗ 未选择文件夹，程序退出{Style.RESET_ALL}")
            return False
            
        try:
            self.current_folder = folder_path
            self.file_list = [f for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f))]
            
            # 创建错误文件夹
            self.error_folder = os.path.join(folder_path, "AI命名错误")
            if not os.path.exists(self.error_folder):
                os.makedirs(self.error_folder)
                print(f"{Fore.GREEN}✓ 已创建错误文件夹: {self.error_folder}{Style.RESET_ALL}")
            
            if not self.file_list:
                print(f"{Fore.YELLOW}! 所选文件夹为空，请选择包含文件的文件夹{Style.RESET_ALL}")
                if messagebox.askyesno("文件夹为空", "所选文件夹不包含任何文件，是否重新选择？"):
                    return self.select_folder()  # 递归调用，让用户重新选择
                return False
                
            print(f"\n{Fore.GREEN}✓ 已加载文件夹: {folder_path}{Style.RESET_ALL}")
            print(f"{Fore.BLUE}i 共找到 {len(self.file_list)} 个文件{Style.RESET_ALL}")
            
            # 显示部分文件名
            if len(self.file_list) > 5:
                sample = self.file_list[:5]
                print(f"{Fore.BLUE}i 部分文件: {', '.join(sample)} 等...{Style.RESET_ALL}")
            else:
                print(f"{Fore.BLUE}i 文件列表: {', '.join(self.file_list)}{Style.RESET_ALL}")
            
            print(f"\n{Fore.YELLOW}> 下一步将使用AI为这些文件生成描述性名称{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}> 提示：请确保您的网络连接稳定，以确保API调用顺利完成{Style.RESET_ALL}")
            
            return True
        except Exception as e:
            print(f"{Fore.RED}✗ 加载文件夹失败: {str(e)}{Style.RESET_ALL}")
            if messagebox.askyesno("错误", f"加载文件夹时出错: {str(e)}\n是否重新选择？"):
                return self.select_folder()  # 递归调用，让用户重新选择
            return False

    def process_sentence(self, index, filename, is_retry=False):
        """处理单个文件的描述生成"""
        try:
            # 获取当前API密钥
            api_key = self.get_next_key()

            # 调用 API 生成描述
            description, usage = self.call_api(filename, api_key)

            # 检查描述长度是否超过限制
            if len(description) > 250:
                error_msg = f"AI生成描述过长 ({len(description)}字符)"
                if not is_retry:
                    # 第一次处理时，将文件移动到错误文件夹，不加入重试队列
                    print(f"{Fore.YELLOW}! 警告: 文件 {filename} 的{error_msg}，将移动到错误文件夹{Style.RESET_ALL}")
                    try:
                        # 构建源文件和目标文件的完整路径
                        src_path = os.path.join(self.current_folder, filename)
                        dst_path = os.path.join(self.error_folder, filename)

                        # 如果目标文件已存在，添加时间戳
                        if os.path.exists(dst_path):
                            name, ext = os.path.splitext(filename)
                            timestamp = int(time.time())
                            dst_path = os.path.join(self.error_folder, f"{name}_{timestamp}{ext}")

                        # 移动文件
                        os.rename(src_path, dst_path)
                        print(f"{Fore.GREEN}✓ 已将文件移动到错误文件夹: {filename}{Style.RESET_ALL}")
                    except Exception as e:
                        print(f"{Fore.RED}✗ 移动文件到错误文件夹失败 ({filename}): {str(e)}{Style.RESET_ALL}")
                return filename, usage.prompt_tokens, usage.completion_tokens

            # 清理描述中的非法字符
            invalid_chars = r'[\\/:*?"<>|]'
            description = re.sub(invalid_chars, '_', description)

            return description, usage.prompt_tokens, usage.completion_tokens
        except Exception as e:
            error_msg = str(e)
            if not is_retry:
                # 只在非重试情况下添加到错误收集器
                self.error_collector.add_failed_file(index, filename, "api_error", error_msg)
            print(f"{Fore.RED}✗ API调用出错 ({filename}): {error_msg}{Style.RESET_ALL}")
            return filename, 0, 0

    def process_retry_files(self):
        """处理重试队列中的文件"""
        retry_files = self.error_collector.get_retry_files()
        if not retry_files:
            return

        print(f"\n{Fore.CYAN}▌ 开始重试失败的文件 ▐{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}> 发现 {len(retry_files)} 个文件需要重试{Style.RESET_ALL}")

        # 重试统计
        retry_success_count = 0
        retry_failed_count = 0

        # 进度显示变量
        bar_width = 40
        total_retry = len(retry_files)
        processed_retry = 0

        # 预先打印一次进度条
        filled_length = 0
        bar = '█' * filled_length + '░' * (bar_width - filled_length)
        print(f"{Fore.BLUE}重试进度: [{bar}] 0% - 0/{total_retry}{Style.RESET_ALL}")

        for retry_info in retry_files:
            index = retry_info['index']
            filename = retry_info['filename']
            retry_count = retry_info['retry_count']

            # 计算重试延迟（指数退避）
            delay = min(2 ** retry_count, 8)  # 最大延迟8秒
            if delay > 0:
                time.sleep(delay)

            print(f"{Fore.YELLOW}> 重试文件 {filename} (第{retry_count + 1}次重试){Style.RESET_ALL}")

            try:
                description, input_tk, output_tk = self.process_sentence(index, filename, is_retry=True)

                # 更新统计信息
                self.input_tokens += input_tk
                self.output_tokens += output_tk

                # 检查是否成功生成描述
                if description != filename:
                    # 成功生成描述
                    formatted_entry = f"{filename}----{description}"
                    self.results[index] = formatted_entry
                    retry_success_count += 1
                    print(f"{Fore.GREEN}✓ 重试成功: {filename}{Style.RESET_ALL}")
                else:
                    # 重试仍然失败，检查是否需要继续重试
                    if self.error_collector.get_retry_count(filename) < self.error_collector.max_retries:
                        # 还可以继续重试，重新加入队列
                        self.error_collector.add_failed_file(index, filename, "api_error", "重试失败")
                    else:
                        # 达到最大重试次数，放弃重试
                        retry_failed_count += 1
                        print(f"{Fore.RED}✗ 重试失败，已达到最大重试次数: {filename}{Style.RESET_ALL}")

            except Exception as e:
                # 重试过程中出现异常
                if self.error_collector.get_retry_count(filename) < self.error_collector.max_retries:
                    self.error_collector.add_failed_file(index, filename, "api_error", str(e))
                else:
                    retry_failed_count += 1
                    print(f"{Fore.RED}✗ 重试异常，已达到最大重试次数: {filename} - {str(e)}{Style.RESET_ALL}")

            # 更新进度显示
            processed_retry += 1
            percent = int(processed_retry * 100 / total_retry)
            filled_length = int(bar_width * processed_retry // total_retry)
            bar = '█' * filled_length + '░' * (bar_width - filled_length)
            print(f"{Fore.BLUE}重试进度: [{bar}] {percent}% - {processed_retry}/{total_retry}{Style.RESET_ALL}")

        # 显示重试统计
        print(f"\n{Fore.GREEN}▌ 重试统计信息 ▐{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─ 重试文件数: {total_retry}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─ 重试成功: {retry_success_count}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─ 重试失败: {retry_failed_count}{Style.RESET_ALL}")

        # 如果还有文件需要重试，递归调用
        if self.error_collector.has_retry_files():
            print(f"{Fore.YELLOW}> 还有文件需要继续重试...{Style.RESET_ALL}")
            self.process_retry_files()



    def _display_statistics(self):
        """显示处理结果统计信息"""
        rate = {"input": 2.0 * 3, "output": 8.0 * 3}  # 固定费率乘3
        cost = (self.input_tokens * rate["input"] / 1e6 + self.output_tokens * rate["output"] / 1e6)
        
        print(f"{Fore.BLUE}▌ 统计信息 ▐{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─ 处理文件数: {self.processed_count}/{len(self.file_list)}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─ 输入Token: {self.input_tokens:,}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─ 输出Token: {self.output_tokens:,}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─ 预估费用: ¥{cost:.4f}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}└─ 使用API密钥: {len(self.active_keys)} 个{Style.RESET_ALL}")

    def _save_results(self):
        """保存处理结果到文本文件（已禁用）"""
        pass  # 按用户要求已禁用结果保存功能

    def _generate_unique_filename(self, base_name, extension):
        """生成唯一的文件名，不修改描述内容"""
        # 先尝试无后缀
        final_name = f"{base_name}{extension}"
        if not os.path.exists(os.path.join(self.current_folder, final_name)):
            return final_name

        # 尝试使用风格后缀
        for suffix in self.STYLE_SUFFIXES:
            style_name = f"{base_name}  {suffix}{extension}"
            if not os.path.exists(os.path.join(self.current_folder, style_name)):
                return style_name

        # 使用数字后缀
        counter = 1
        while True:
            final_name = f"{base_name}_{counter}{extension}"
            if not os.path.exists(os.path.join(self.current_folder, final_name)):
                return final_name
            counter += 1

    def _rename_files(self):
        """重命名处理过的文件"""
        if not self.results or not self.current_folder:
            print(f"{Fore.YELLOW}! 没有可用的重命名数据{Style.RESET_ALL}")
            return
        
        try:
            print(f"\n{Fore.CYAN}▌ 开始重命名文件 ▐{Style.RESET_ALL}")
            
            # 创建重命名任务列表
            rename_tasks = []
            for entry in self.results:
                if entry and "----" in entry:
                    old_name, new_name = entry.split("----", 1)
                    
                    # 基本验证
                    if not new_name or len(new_name.strip()) == 0:
                        print(f"{Fore.YELLOW}! 跳过一个文件: 新名称为空{Style.RESET_ALL}")
                        continue
                    
                    # 如果新名称与旧名称相同，说明是保留原文件名的情况
                    # 检查文件是否已经被移动到错误文件夹
                    src_path = os.path.join(self.current_folder, old_name)
                    if not os.path.exists(src_path):
                        print(f"{Fore.BLUE}i 文件 {old_name} 已被移动到错误文件夹，跳过重命名{Style.RESET_ALL}")
                        continue
                    
                    # 随机替换前缀
                    new_name = self._replace_prefix_randomly(new_name)

                    # 清理文件名中的非法字符和不可见字符
                    new_name = self._clean_filename(new_name)

                    # 如果清理后为空，保留原文件名
                    if not new_name:
                        continue
                    
                    rename_tasks.append((old_name, new_name))
            
            print(f"{Fore.BLUE}i 准备重命名 {len(rename_tasks)} 个文件{Style.RESET_ALL}")
            
            if len(rename_tasks) == 0:
                print(f"{Fore.YELLOW}! 没有有效的重命名任务{Style.RESET_ALL}")
                return
            
            # 执行重命名
            print(f"{Fore.BLUE}i 正在执行重命名...{Style.RESET_ALL}")
            success_count = 0
            failure_count = 0
            retry_count = 0
            max_retries = 3
            
            # 显示进度条的变量
            bar_width = 40  # 进度条宽度
            total = len(rename_tasks)
            processed = 0
            
            for old_name, new_name in rename_tasks:
                try:
                    processed += 1
                    original_extension = os.path.splitext(old_name)[1]
                    
                    # 生成唯一文件名
                    final_name = self._generate_unique_filename(new_name, original_extension)
                    
                    # 显示进度条
                    percent = int(processed * 100 / total)
                    filled_length = int(bar_width * processed // total)
                    bar = '█' * filled_length + '░' * (bar_width - filled_length)
                    sys.stdout.write(f"\r{Fore.BLUE}重命名进度: [{bar}] {percent}% - {processed}/{total}{Style.RESET_ALL}")
                    sys.stdout.flush()
                    
                    # 执行重命名（带重试机制）
                    success = False
                    for attempt in range(max_retries):
                        try:
                            src = os.path.join(self.current_folder, old_name)
                            dst = os.path.join(self.current_folder, final_name)
                            
                            # 检查源文件是否存在
                            if not os.path.exists(src):
                                failure_count += 1  # 增加失败计数
                                # 这里直接跳出循环，保留原文件名
                                break
                                
                            os.rename(src, dst)
                            success = True
                            break
                        except Exception as e:
                            if attempt < max_retries - 1:
                                time.sleep(0.1)  # 短暂等待后重试
                                retry_count += 1
                            else:
                                # 最后一次尝试失败，记录错误并保留原文件名
                                failure_count += 1
                                
                    if success:
                        success_count += 1
                    else:
                        # 如果没有成功，确认记录失败信息
                        print(f"\r{Fore.YELLOW}! 文件重命名失败，保留原文件名{' ' * 30}{Style.RESET_ALL}")
                except Exception as e:
                    failure_count += 1
            
            # 清空当前行        
            sys.stdout.write('\r' + ' ' * 100 + '\r')
            sys.stdout.flush()
            
            # 重命名完成报告
            print(f"\n{Fore.GREEN}▌ 重命名统计信息 ▐{Style.RESET_ALL}")
            print(f"{Fore.CYAN}└─ 总文件数: {total}{Style.RESET_ALL}")
            print(f"{Fore.CYAN}└─ 成功重命名: {success_count}{Style.RESET_ALL}")
            print(f"{Fore.CYAN}└─ 重命名失败: {failure_count}{Style.RESET_ALL}")
            print(f"{Fore.CYAN}└─ 重试次数: {retry_count}{Style.RESET_ALL}")
            
            print(f"\n{Fore.YELLOW}> 处理已全部完成，成功重命名的文件已使用AI生成的描述性名称{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}> 重命名失败的文件已保留原文件名{Style.RESET_ALL}")
            
        except Exception as e:
            print(f"{Fore.RED}✗ 重命名过程出错: {str(e)}{Style.RESET_ALL}")
            import traceback
            print(f"{Fore.RED}{traceback.format_exc()}{Style.RESET_ALL}")

    def _display_key_usage_statistics(self):
        """显示API密钥使用次数统计"""
        if not self.key_usage_count:
            print(f"{Fore.YELLOW}! 没有API密钥使用记录{Style.RESET_ALL}")
            return
            
        print(f"\n{Fore.CYAN}▌ API密钥使用统计 ▐{Style.RESET_ALL}")
        
        # 按使用次数降序排序
        sorted_usage = sorted(self.key_usage_count.items(), key=lambda x: x[1], reverse=True)
        
        total_usage = sum(count for _, count in sorted_usage)
        
        for key, count in sorted_usage:
            if key in self.keys_status:
                user_name = self.keys_status[key].get("name", "未知用户")
                key_short = f"{key[:8]}...{key[-8:]}"
                percentage = (count / total_usage) * 100 if total_usage > 0 else 0
                print(f"{Fore.CYAN}└─ 密钥 {key_short} ({user_name}): {count} 次调用 ({percentage:.1f}%){Style.RESET_ALL}")
            
        print(f"{Fore.CYAN}└─ 总调用次数: {total_usage}{Style.RESET_ALL}")

    def start_processing(self):
        """开始处理所有文件"""
        if not self.file_list:
            print(f"{Fore.RED}✗ 没有文件需要处理{Style.RESET_ALL}")
            return False

        if not self.active_keys:
            print(f"{Fore.RED}✗ 没有可用的API密钥，无法处理文件{Style.RESET_ALL}")
            return False
        
        try:
            print(f"\n{Fore.CYAN}▌ 处理确认 ▐{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}> 程序将使用AI为 {len(self.file_list)} 个文件生成描述性名称{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}> 处理过程中请勿关闭程序，完成后将自动为文件重命名{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}> 如需中断处理，可按 Ctrl+C{Style.RESET_ALL}")
            
            # 使用tkinter弹窗询问确认
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            confirm = messagebox.askyesno(
                "处理确认", 
                f"程序将使用AI为 {len(self.file_list)} 个文件生成描述性名称。\n\n是否开始处理？"
            )
            root.destroy()
            
            if not confirm:
                print(f"{Fore.YELLOW}! 已取消处理{Style.RESET_ALL}")
                return False
            
            # 根据API密钥数量和文件数量自动分配线程
            # 默认50个线程，但不超过密钥数量和文件数量
            available_keys = len(self.active_keys)
            total_files = len(self.file_list)
            self.thread_count = min(50, available_keys, total_files)
            
            print(f"\n{Fore.GREEN}▶ 开始处理，使用 {self.thread_count} 个线程，{available_keys} 个API密钥{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}> 正在处理文件，请稍候...{Style.RESET_ALL}")
            
            self.is_processing = True
            self.input_tokens = 0
            self.output_tokens = 0
            self.results = [None] * len(self.file_list)  # 预分配结果列表
            self.processed_count = 0
            self.invalid_responses = 0  # 记录无效响应数量
            self.error_collector.clear()  # 清空错误收集器
            
            start_time = time.time()
            
            # 进度显示相关变量
            bar_width = 40  # 进度条宽度
            
            # 预先打印一次进度条
            filled_length = 0
            bar = '█' * filled_length + '░' * (bar_width - filled_length)
            print(f"{Fore.BLUE}进度: [{bar}] 0% - 0/{total_files}{Style.RESET_ALL}")
            
            # 创建进度显示线程，与主处理线程分离
            stop_progress_thread = False
            progress_lock = threading.Lock()
            
            def update_progress_display():
                last_count = 0
                while not stop_progress_thread and self.is_processing:
                    with progress_lock:
                        current_count = self.processed_count
                        if current_count != last_count:
                            percent = int(current_count * 100 / total_files)
                            filled_length = int(bar_width * current_count // total_files)
                            bar = '█' * filled_length + '░' * (bar_width - filled_length)
                            print(f"{Fore.BLUE}进度: [{bar}] {percent}% - {current_count}/{total_files}{Style.RESET_ALL}")
                            last_count = current_count
                    time.sleep(0.1)  # 短暂休眠，避免CPU占用过高
            
            # 启动进度显示线程
            progress_thread = threading.Thread(target=update_progress_display)
            progress_thread.daemon = True
            progress_thread.start()
            
            try:
                with concurrent.futures.ThreadPoolExecutor(max_workers=self.thread_count) as executor:
                    futures = {}
                    for idx, filename in enumerate(self.file_list):
                        if not self.is_processing:
                            break
                        future = executor.submit(self.process_sentence, idx, filename)
                        futures[future] = (idx, filename)
                    
                    # 处理完成的结果
                    for future in concurrent.futures.as_completed(futures):
                        idx, filename = futures[future]
                        try:
                            description, input_tk, output_tk = future.result()
                            
                            # 检查描述是否与原文件名相同（表示生成失败）
                            if description == filename:
                                with progress_lock:
                                    self.invalid_responses += 1
                            
                            # 更新统计信息
                            with progress_lock:
                                self.processed_count += 1
                                self.input_tokens += input_tk
                                self.output_tokens += output_tk
                                
                                # 格式化输出条目
                                formatted_entry = f"{filename}----{description}"
                                self.results[idx] = formatted_entry
                        except Exception as e:
                            with progress_lock:
                                self.processed_count += 1
                                self.invalid_responses += 1
                                print(f"\r{Fore.RED}✗ 处理文件 '{filename}' 时出错: {str(e)}{' ' * 30}{Style.RESET_ALL}")
                                # 确保在异常情况下也添加结果，保留原文件名
                                self.results[idx] = f"{filename}----{filename}"
            finally:
                # 停止进度显示线程
                stop_progress_thread = True
                progress_thread.join(timeout=1.0)  # 等待进度线程结束，最多等待1秒
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 打印最终进度
            bar = '█' * bar_width
            print(f"{Fore.BLUE}进度: [{bar}] 100% - {total_files}/{total_files}{Style.RESET_ALL}")
            
            # 打印结果统计
            print(f"\n{Fore.GREEN}✓ 处理完成! 耗时: {duration:.2f}秒{Style.RESET_ALL}")
            if self.invalid_responses > 0:
                print(f"{Fore.YELLOW}! 注意: {self.invalid_responses}/{total_files} 个文件的描述生成失败或无效，将保留原文件名{Style.RESET_ALL}")
            self._display_statistics()
            
            # 显示API密钥使用统计
            self._display_key_usage_statistics()

            # 处理重试队列中的失败文件
            if self.error_collector.has_retry_files():
                print(f"\n{Fore.YELLOW}> 检测到失败的文件，开始重试处理...{Style.RESET_ALL}")
                self.process_retry_files()

                # 重试完成后更新统计信息
                print(f"\n{Fore.GREEN}▌ 重试完成后的最终统计 ▐{Style.RESET_ALL}")
                self._display_statistics()

                # 显示最终失败的文件
                final_failed_files = self.error_collector.get_final_failed_files()
                if final_failed_files:
                    print(f"\n{Fore.RED}▌ 最终失败的文件 ▐{Style.RESET_ALL}")
                    print(f"{Fore.YELLOW}> 以下 {len(final_failed_files)} 个文件经过多次重试后仍然失败：{Style.RESET_ALL}")
                    for failed_file in final_failed_files:
                        print(f"{Fore.RED}  - {failed_file}{Style.RESET_ALL}")
                    print(f"{Fore.YELLOW}> 这些文件将保留原文件名{Style.RESET_ALL}")
            else:
                print(f"\n{Fore.GREEN}✓ 所有文件处理成功，无需重试{Style.RESET_ALL}")

            # 执行重命名
            self._rename_files()

            return True
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}! 用户中断处理{Style.RESET_ALL}")
            self.is_processing = False
            return False
        except Exception as e:
            print(f"\n{Fore.RED}✗ 处理过程出错: {str(e)}{Style.RESET_ALL}")
            self.is_processing = False
            return False

    def _replace_prefix_randomly(self, filename):
        """随机替换文件名中的前缀"""
        # 定义需要替换的原始前缀（不区分大小写）
        original_prefix = "2D Flat Rotatable acrylic desktop ornament"

        # 检查文件名是否包含原始前缀（不区分大小写）
        filename_lower = filename.lower()
        original_prefix_lower = original_prefix.lower()

        if original_prefix_lower in filename_lower:
            # 找到原始前缀在文件名中的位置
            start_index = filename_lower.find(original_prefix_lower)
            end_index = start_index + len(original_prefix)

            # 随机选择一个新的前缀
            new_prefix = random.choice(self.PREFIX_OPTIONS)

            # 替换前缀
            new_filename = filename[:start_index] + new_prefix + filename[end_index:]

            print(f"{Fore.CYAN}i 随机替换前缀: {original_prefix} → {new_prefix}{Style.RESET_ALL}")
            return new_filename

        return filename

    def _clean_filename(self, filename):
        """清理文件名中的非法字符和不可见字符"""
        # 处理换行符、回车符和制表符等不可见字符
        filename = filename.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')

        # 替换Windows不允许的字符
        invalid_chars = r'[\\/:*?"<>|]'
        filename = re.sub(invalid_chars, '_', filename)

        # 处理其他不可见字符（ASCII码小于32的控制字符）
        filename = ''.join(c if ord(c) >= 32 else '_' for c in filename)

        # 去除首尾空格
        filename = filename.strip()

        # 确保文件名不为空
        if not filename:
            print(f"{Fore.YELLOW}! 警告: 清理后文件名为空，将使用原文件名{Style.RESET_ALL}")
            return ""  # 返回空字符串，调用方会处理为保留原文件名

        return filename

def main():
    """主函数"""
    print(f"{Fore.CYAN}======================================{Style.RESET_ALL}")
    print(f"{Fore.CYAN}      AI产品描述生成器 (命令行版)      {Style.RESET_ALL}")
    print(f"{Fore.CYAN}======================================{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}欢迎使用AI产品描述生成器! 本工具可以使用AI技术为您的文件生成专业的描述性名称。{Style.RESET_ALL}")

    try:
        # 创建生成器实例
        generator = AINameGenerator()

        # 显示当前使用的AI模型
        model_name = generator.config.get('model', '未知模型')
        print(f"{Fore.GREEN}当前使用的AI模型: {model_name}{Style.RESET_ALL}")

        print(f"{Fore.YELLOW}使用说明:{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}1. 选择包含待处理文件的文件夹{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}2. 程序将使用AI为每个文件生成描述性名称{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}3. 最后程序将为文件重命名{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}提示: 处理过程中可随时按Ctrl+C中断操作{Style.RESET_ALL}")
        print(f"{Fore.CYAN}======================================{Style.RESET_ALL}")

        # 处理循环
        continue_processing = True
        while continue_processing:
            # 选择文件夹
            if not generator.select_folder():
                print(f"\n{Fore.YELLOW}程序已退出，未开始处理文件{Style.RESET_ALL}")
                break

            # 开始处理
            result = generator.start_processing()
            if result:
                print(f"\n{Fore.GREEN}✓ 所有操作已完成!{Style.RESET_ALL}")
            
            # 询问是否继续处理其他文件夹
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            continue_processing = messagebox.askyesno(
                "继续处理", 
                "是否继续选择其他文件夹进行处理？"
            )
            root.destroy()
            
            if continue_processing:
                print(f"\n{Fore.CYAN}======================================{Style.RESET_ALL}")
                print(f"{Fore.CYAN}      准备处理下一个文件夹      {Style.RESET_ALL}")
                print(f"{Fore.CYAN}======================================{Style.RESET_ALL}")
            else:
                print(f"\n{Fore.CYAN}======================================{Style.RESET_ALL}")
                print(f"{Fore.CYAN}             程序已退出             {Style.RESET_ALL}")
                print(f"{Fore.CYAN}======================================{Style.RESET_ALL}")
        
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}! 程序被用户中断{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}✗ 程序发生错误: {str(e)}{Style.RESET_ALL}")
    finally:
        print(f"{Fore.CYAN}======================================{Style.RESET_ALL}")
        print(f"{Fore.CYAN}             程序已退出             {Style.RESET_ALL}")
        print(f"{Fore.CYAN}======================================{Style.RESET_ALL}")
        input(f"{Fore.GREEN}按回车键关闭窗口...{Style.RESET_ALL}")

if __name__ == "__main__":
    main() 